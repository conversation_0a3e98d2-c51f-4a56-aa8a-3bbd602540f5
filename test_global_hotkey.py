#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全局快捷键功能的简单脚本
"""

import tkinter as tk
from tkinter import messagebox
import win32api
import time
import threading

class GlobalHotkeyTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("全局快捷键测试")
        self.root.geometry("500x400")
        
        self.running = False
        self.last_ctrl_1_time = 0
        
        # 创建界面
        self.setup_ui()
        
        # 设置快捷键
        self.setup_hotkeys()
    
    def setup_ui(self):
        """设置用户界面"""
        tk.Label(self.root, text="全局快捷键测试程序", font=("微软雅黑", 16)).pack(pady=20)
        
        tk.Label(self.root, text="按 Ctrl+1 测试全局快捷键功能", font=("微软雅黑", 12)).pack(pady=10)
        tk.Label(self.root, text="即使窗口失去焦点也应该能工作", font=("微软雅黑", 10), fg="gray").pack(pady=5)
        
        self.start_button = tk.Button(self.root, text="开始测试", command=self.start_test, font=("微软雅黑", 12))
        self.start_button.pack(pady=10)
        
        self.stop_button = tk.Button(self.root, text="停止测试", command=self.stop_test, font=("微软雅黑", 12))
        self.stop_button.pack(pady=10)
        self.stop_button.config(state=tk.DISABLED)
        
        self.status_label = tk.Label(self.root, text="状态: 未开始", font=("微软雅黑", 10))
        self.status_label.pack(pady=20)
        
        # 日志显示区域
        self.log_text = tk.Text(self.root, height=10, width=60)
        self.log_text.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def setup_hotkeys(self):
        """设置快捷键"""
        try:
            # 绑定tkinter快捷键（作为备用）
            self.root.bind_all('<Control-Key-1>', self.hotkey_triggered)
            self.root.bind_all('<Control-1>', self.hotkey_triggered)
            
            self.root.focus_set()
            self.log("快捷键已设置: Ctrl+1")
        except Exception as e:
            self.log(f"设置快捷键失败: {e}")
    
    def start_test(self):
        """开始测试"""
        self.running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_label.config(text="状态: 测试中 - 请按 Ctrl+1")
        
        self.log("开始测试全局快捷键...")
        self.log("请尝试按 Ctrl+1")
        self.log("可以点击其他窗口测试全局性")
        
        # 启动全局快捷键监控
        self.start_global_monitor()
        
        # 启动模拟工作线程
        threading.Thread(target=self.simulate_work, daemon=True).start()
    
    def stop_test(self):
        """停止测试"""
        self.running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="状态: 已停止")
        
        self.log("测试已停止")
    
    def start_global_monitor(self):
        """启动全局快捷键监控"""
        threading.Thread(target=self.global_hotkey_monitor, daemon=True).start()
        self.log("全局快捷键监控已启动")
    
    def global_hotkey_monitor(self):
        """全局快捷键监控线程"""
        self.log("开始监控全局快捷键 Ctrl+1...")
        
        # 记录按键状态
        ctrl_was_pressed = False
        key_1_was_pressed = False
        
        while self.running:
            try:
                # 检查按键状态
                ctrl_pressed = win32api.GetAsyncKeyState(0x11) & 0x8000  # VK_CONTROL
                key_1_pressed = win32api.GetAsyncKeyState(0x31) & 0x8000  # VK_1
                
                # 检测按键按下事件（从未按下到按下）
                if ctrl_pressed and key_1_pressed and not (ctrl_was_pressed and key_1_was_pressed):
                    current_time = time.time()
                    # 防止重复触发
                    if current_time - self.last_ctrl_1_time > 0.5:
                        self.last_ctrl_1_time = current_time
                        self.root.after(0, self.hotkey_triggered)
                
                # 更新按键状态
                ctrl_was_pressed = bool(ctrl_pressed)
                key_1_was_pressed = bool(key_1_pressed)
                
                # 短暂休眠，避免过度占用CPU
                time.sleep(0.05)  # 50ms检查一次
                
            except Exception as e:
                self.log(f"全局快捷键监控出错: {e}")
                time.sleep(0.1)
        
        self.log("全局快捷键监控已停止")
    
    def hotkey_triggered(self, event=None):
        """快捷键被触发"""
        self.log("🎉 检测到 Ctrl+1 快捷键！")
        if self.running:
            self.stop_test()
            messagebox.showinfo("成功", "全局快捷键 Ctrl+1 工作正常！\n测试已停止。")
        return "break"
    
    def simulate_work(self):
        """模拟工作过程"""
        count = 0
        while self.running:
            count += 1
            self.root.after(0, lambda c=count: self.status_label.config(text=f"状态: 测试中 ({c}s) - 请按 Ctrl+1"))
            time.sleep(1)
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = GlobalHotkeyTest()
    app.run()
