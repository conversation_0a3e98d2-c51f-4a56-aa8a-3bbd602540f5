import pyautogui
import pygetwindow as gw
from pywinauto import Application
import uiautomation as auto
import time
from datetime import datetime
from typing import Dict, List, Any
import pandas as pd
import os
import re
import jieba
import jieba.analyse
from collections import Counter
import tkinter as tk
from tkinter import scrolledtext, messagebox, ttk
import threading
import sys
import subprocess
import pyperclip
import win32gui
import win32con
import win32api  # 添加win32api用于检测按键状态
import ctypes
from ctypes import wintypes
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 导入所需模块
from src.config import client_config
from src.MachineCodeGenerator import MachineCodeGenerator
from src.youdaoYun import YouDaoNote

# Helper class to redirect stdout to a tkinter Text widget
class StdoutRedirector:
    def __init__(self, text_widget):
        self.text_widget = text_widget
        self.text_widget.configure(state='disabled') # Make it read-only initially

    def write(self, string):
        self.text_widget.configure(state='normal')
        self.text_widget.insert(tk.END, string)
        self.text_widget.see(tk.END) # Scroll to the end
        self.text_widget.configure(state='disabled')

    def flush(self):
        pass # sys.stdout.flush() will be called anyway

class MomentData:
    def __init__(self):
        self.author = ""
        self.content = ""
        self.publish_time = ""
        self.image_count = 0
        self.likes = []
        self.comments = []
        self.replies = []  # 存储回复信息

def find_wechat_window():
    """查找微信朋友圈窗口"""
    windows = gw.getAllWindows()
    for window in windows:
        if ('朋友圈' in window.title and 
            'Visual Studio Code' not in window.title and 
            'Code' not in window.title):
            return window
    return None

def analyze_content(text):
    """分析文案内容特点"""
    # 提取关键词
    keywords = jieba.analyse.extract_tags(text, topK=10, withWeight=True)
    
    # 分析文案类型
    content_type = "未知"
    if "我" in text and ("经历" in text or "经验" in text or "故事" in text):
        content_type = "个人经历分享"
    if "教育" in text or "学习" in text or "培训" in text or "课程" in text:
        content_type += "+教育服务推广" if content_type != "未知" else "教育服务推广"
    if "产品" in text or "优惠" in text or "折扣" in text or "限时" in text:
        content_type += "+产品促销" if content_type != "未知" else "产品促销"
    
    # 分析写作风格
    style = []
    if len(text) > 500:
        style.append("详细")
    else:
        style.append("简洁")
    
    if "！" in text or "？" in text:
        style.append("情感化")
    
    if re.search(r'\d+', text):
        style.append("数据支持")
    
    if "我" in text or "我们" in text:
        style.append("第一人称")
    
    # 提取核心卖点
    selling_points = []
    if re.search(r'(\d+)年', text):
        years = re.findall(r'(\d+)年', text)
        if years:
            selling_points.append(f"教学经验（{years[0]}年）")
    
    if re.search(r'(\d+)分', text):
        scores = re.findall(r'(\d+)分', text)
        if len(scores) >= 2:
            selling_points.append(f"学生成功案例（{min(scores)}分到{max(scores)}分的逆袭）")
    
    return {
        "keywords": keywords,
        "content_type": content_type,
        "style": style,
        "selling_points": selling_points
    }

def classify_users(users):
    """对用户进行分类"""
    parent_keywords = ["妈妈", "爸爸", "家长", "宝妈", "宝爸"]
    education_keywords = ["老师", "园长", "学校", "幼儿园", "教育", "培训"]
    
    parents = []
    educators = []
    others = []
    
    for user in users:
        if any(keyword in user for keyword in parent_keywords):
            parents.append(user)
        elif any(keyword in user for keyword in education_keywords):
            educators.append(user)
        else:
            others.append(user)
    
    return {
        "parents": parents,
        "educators": educators,
        "others": others
    }

def analyze_comments(comments, replies):
    """分析评论内容"""
    # 找出哪些评论有回复
    replied_authors = set()
    for reply in replies:
        if "回复" in reply:
            parts = reply.split("回复")
            if len(parts) > 1:
                replied_authors.add(parts[1].strip())
    
    # 分析评论情感
    positive_keywords = ["好", "棒", "赞", "支持", "厉害", "强"]
    negative_keywords = ["差", "不好", "失望", "退款"]
    
    sentiment_stats = {"positive": 0, "neutral": 0, "negative": 0}
    
    for comment in comments:
        content = comment.get("content", "")
        is_positive = any(keyword in content for keyword in positive_keywords)
        is_negative = any(keyword in content for keyword in negative_keywords)
        
        if is_positive and not is_negative:
            sentiment_stats["positive"] += 1
        elif is_negative and not is_positive:
            sentiment_stats["negative"] += 1
        else:
            sentiment_stats["neutral"] += 1
    
    return {
        "replied_authors": replied_authors,
        "sentiment_stats": sentiment_stats
    }

def generate_report_text(moment: MomentData):
    """生成分析报告文本"""
    content_analysis = analyze_content(moment.content)
    user_classification = classify_users(moment.likes)
    comment_analysis = analyze_comments(moment.comments, moment.replies)
    
    report = []
    report.append("### 朋友圈内容分析\n")
    report.append("#### 1. 基本信息")
    report.append("| 项目 | 内容 |")
    report.append("|------|------|")
    report.append(f"| 发布者 | {moment.author} |")
    report.append(f"| 发布时间 | {moment.publish_time} |")
    report.append(f"| 图片数量 | {moment.image_count}张 |")
    report.append(f"| 文案字数 | {len(moment.content)}字 |")
    report.append("")
    report.append("#### 2. 文案内容")
    report.append("```")
    if len(moment.content) > 400:
        report.append(moment.content[:300] + "\n\n[文案内容省略...]\n\n" + moment.content[-100:])
    else:
        report.append(moment.content)
    report.append("```\n")
    report.append("#### 3. 互动统计")
    report.append("| 类型 | 数量 |")
    report.append("|------|------|")
    report.append(f"| 点赞总人数 | {len(moment.likes)}+ |")
    report.append(f"| 评论总数 | {len(moment.comments)}+ |")
    report.append(f"| 评论互动次数 | {len(moment.replies) + len(moment.comments)}+ |")
    report.append("")
    report.append("#### 4. 评论分析（部分展示）")
    report.append("| 评论者 | 评论内容 | 评论时间 | 是否有回复 |")
    report.append("|--------|----------|-----------|------------|")
    for i, comment in enumerate(moment.comments[:5]):
        has_reply = "是" if comment["author"] in comment_analysis["replied_authors"] else "否"
        report.append(f"| {comment['author']} | {comment['content'][:30]}{'...' if len(comment['content']) > 30 else ''} | {comment['time']} | {has_reply} |")
    report.append("")
    report.append("#### 5. 点赞用户分类（按类型）")
    total_likes = len(moment.likes)
    if total_likes > 0:
        parent_percent = round(len(user_classification["parents"]) / total_likes * 100)
        educator_percent = round(len(user_classification["educators"]) / total_likes * 100)
        other_percent = 100 - parent_percent - educator_percent
        report.append(f"1. 家长用户（约{parent_percent}%）")
        if user_classification["parents"]:
            report.append(f"   - 示例：{', '.join(user_classification['parents'][:3])}等")
        report.append(f"2. 教育从业者（约{educator_percent}%）")
        if user_classification["educators"]:
            report.append(f"   - 示例：{', '.join(user_classification['educators'][:3])}等")
        report.append(f"3. 其他用户（约{other_percent}%）")
        if user_classification["others"]:
            report.append(f"   - 示例：{', '.join(user_classification['others'][:3])}等")
    report.append("")
    report.append("#### 6. 文案特点分析")
    report.append(f"1. 内容类型：{content_analysis['content_type']}")
    report.append(f"2. 写作风格：{', '.join(content_analysis['style'])}")
    report.append("3. 核心卖点：")
    for point in content_analysis["selling_points"]:
        report.append(f"   - {point}")
    if not content_analysis["selling_points"]:
        report.append("   - 未明确提取到核心卖点")
    report.append("")
    report.append("#### 7. 互动效果分析")
    sentiment = comment_analysis["sentiment_stats"]
    total_sentiment = sum(sentiment.values())
    if total_sentiment > 0:
        positive_percent = round(sentiment["positive"] / total_sentiment * 100)
        if positive_percent > 70:
            report.append("- 评论质量：以正面支持为主")
        elif positive_percent > 40:
            report.append("- 评论质量：正面评价与中性评价并存")
        else:
            report.append("- 评论质量：评价较为中性")
    if len(comment_analysis["replied_authors"]) > len(moment.comments) * 0.5:
        report.append("- 互动深度：发布者积极回复，互动率高")
    elif len(comment_analysis["replied_authors"]) > 0:
        report.append("- 互动深度：发布者有回复，互动率中等")
    else:
        report.append("- 互动深度：发布者回复较少，互动率低")
    report.append("- 转发/分享：未显示具体数据")
    return "\n".join(report)
    
def save_report_to_file(report_text: str, filename: str = "朋友圈分析报告.txt"):
    """将报告文本写入文件"""
    with open(filename, "w", encoding="utf-8") as f:
        f.write(report_text)
    return os.path.abspath(filename)

def save_to_excel(moment: MomentData, filename: str = "朋友圈数据.xlsx"):
    """保存数据到Excel文件"""
    # 创建一个Excel写入器
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 基本信息表
        basic_info = pd.DataFrame({
            '项目': ['发布者', '发布时间', '图片数量', '文案字数'],
            '内容': [
                moment.author,
                moment.publish_time,
                f"{moment.image_count}张",
                f"{len(moment.content)}字"
            ]
        })
        basic_info.to_excel(writer, sheet_name='基本信息', index=False)
        
        # 文案内容表
        content_df = pd.DataFrame({
            '文案内容': [moment.content]
        })
        content_df.to_excel(writer, sheet_name='文案内容', index=False)
        
        # 点赞用户表
        if moment.likes:
            likes_df = pd.DataFrame({
                '序号': range(1, len(moment.likes) + 1),
                '用户昵称': moment.likes
            })
            likes_df.to_excel(writer, sheet_name='点赞用户', index=False)
        
        # 评论列表表
        if moment.comments:
            # 添加是否有回复列
            comment_analysis = analyze_comments(moment.comments, moment.replies)
            processed_comments = []
            for c in moment.comments:
                new_comment = c.copy() # Avoid modifying original
                new_comment["是否有回复"] = "是" if c["author"] in comment_analysis["replied_authors"] else "否"
                processed_comments.append(new_comment)
            
            comments_df = pd.DataFrame(processed_comments)
            comments_df.to_excel(writer, sheet_name='评论列表', index=False)
        
        # 用户分类表
        if moment.likes:
            user_classification = classify_users(moment.likes)
            user_types = []
            for user in moment.likes:
                if user in user_classification["parents"]:
                    user_types.append("家长")
                elif user in user_classification["educators"]:
                    user_types.append("教育从业者")
                else:
                    user_types.append("其他")
            
            user_df = pd.DataFrame({
                '用户昵称': moment.likes,
                '用户类型': user_types
            })
            user_df.to_excel(writer, sheet_name='用户分类', index=False)
    return os.path.abspath(filename)

def collect_moment_data(control, moment: MomentData, level=0, path=""):
    """收集朋友圈数据"""
    try:
        if not control or not hasattr(control, 'Exists'):
            return
        
        if not control.Exists(maxSearchSeconds=1):
            return
        
        # 获取控件名称和类型
        name = getattr(control, 'Name', '')
        # control_type = getattr(control, 'ControlType', '') # Not used in this function
        
        # 调试信息
        # current_path = f"{path}/{level}-{name[:20]}" if name else f"{path}/{level}-无名称" # Not used
        
        if not name:
            # 如果没有名称，直接处理子控件
            try:
                children = control.GetChildren()
                if children:
                    for i, child in enumerate(children): # Added enumerate for original logic
                        collect_moment_data(child, moment, level + 1, f"{path}/{i}") # Corrected path
            except Exception:
                pass
            return
        
        # 处理控件数据
        if level <= 1 and len(name) > 5 and "朋友圈" not in name:  # 可能是作者信息
            if not moment.author: # Only set if not already set
                moment.author = name
        
        if "天前" in name or "小时前" in name or "分钟前" in name:  # 发布时间
            moment.publish_time = name # Overwrites if found multiple times, last one wins
        
        if "图片" in name and "张" in name:  # 图片数量
            try:
                count_text = name.split("张")[0] # Renamed for clarity
                moment.image_count = int(''.join(filter(str.isdigit, count_text)))
            except ValueError:
                pass # Silently ignore if parsing fails
        
        # 主要内容（通常较长）
        if level <= 2 and len(name) > 100 and not moment.content: # Only set if not already set
            moment.content = name
        
        # 收集评论和回复
        if "回复" in name and len(name) < 100: # Heuristic for replies
            moment.replies.append(name)
        elif "：" in name and len(name) < 200:  # Heuristic for comments
            try:
                author, content = name.split("：", 1)
                # 检查是否已存在相同作者的评论 (This logic might be too simple, duplicates can happen)
                if not any(c["author"] == author.strip() for c in moment.comments):
                    moment.comments.append({
                        "author": author.strip(),
                        "content": content.strip(),
                        "time": "未知" # Timestamp for comments is not directly available
                    })
            except ValueError: # If split fails
                pass
        
        # 收集点赞用户
        if level > 2 and len(name) < 50 and name not in moment.likes:
            # 排除一些常见的非用户名文本
            exclude_words = ["朋友圈", "图片", "评论", "点赞", "分享", "删除", "举报", "全文"]
            if not any(word in name for word in exclude_words) and "：" not in name:
                moment.likes.append(name)
        
        # 递归处理子控件
        try:
            children = control.GetChildren()
            if children:
                for i, child in enumerate(children): # Added enumerate for original logic
                    collect_moment_data(child, moment, level + 1, f"{path}/{i}") # Corrected path
        except Exception: # Broad exception, consider more specific handling
            pass
            
    except Exception as e:
        # This print will go to original stdout, not GUI unless main_logic is careful
        print(f'收集数据时出错: {str(e)}')

def check_and_install_dependencies():
    """检查并安装所需的依赖包"""
    dependencies = {
        'jieba': '用于中文分词和关键词提取',
        'pyperclip': '用于剪贴板操作',
        'pywin32': '用于Windows系统操作'
    }
    
    missing_deps = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} 已安装")
        except ImportError:
            missing_deps.append(dep)
            print(f"✗ {dep} 未安装 ({dependencies[dep]})")
    
    if not missing_deps:
        return True

    print("\n正在安装缺失的依赖...")
    for dep in missing_deps:
        try:
            subprocess.check_call(
                [sys.executable, "-m", "pip", "install", dep],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            print(f"✓ {dep} 安装成功")
            # messagebox.showinfo("安装成功", f"{dep} 已成功安装！")
        except subprocess.CalledProcessError as e:
            error_msg = f"安装 {dep} 时出错：{str(e)}"
            print(f"✗ {error_msg}")
            messagebox.showerror("安装失败", f"{error_msg}\n\n请尝试手动安装：\npip install {dep}")
            return False
            
    messagebox.showinfo("依赖安装完成", "所有缺失的依赖已安装完毕，请重启程序。")
    return True

def main_logic(app_instance):
    """Main logic for data collection and analysis, adapted for GUI."""
    app_instance.update_status_text("开始分析...\n")

    if not check_and_install_dependencies():
        app_instance.update_status_text("依赖问题，请处理后重试。\n")
        app_instance.analysis_done()
        return

    app_instance.update_status_text("正在查找朋友圈窗口...\n")
    window = find_wechat_window()
    
    if not window:
        app_instance.update_status_text("未找到朋友圈窗口，请确保朋友圈窗口已打开。\n")
        messagebox.showwarning("未找到窗口", "未找到朋友圈窗口，请确保朋友圈窗口已打开。")
        app_instance.analysis_done()
        return
    
    app_instance.update_status_text(f"找到窗口标题: {window.title}\n")
    
    try:
        app_instance.update_status_text("\n正在激活窗口...\n")
        window.activate()
        time.sleep(2) # Increased sleep after activation
        
        app_instance.update_status_text("正在获取窗口控件...\n")
        # Ensure uiautomation is looking for the right window.
        # Sometimes title might change slightly or focus issues.
        # A more robust way could be to use process ID if available.
        wechat_window_control = auto.WindowControl(searchDepth=1, Name=window.title)
        
        if not wechat_window_control.Exists(maxSearchSeconds=10): # Increased timeout
            app_instance.update_status_text("无法获取窗口控件，请确保窗口正常显示且未被遮挡。\n")
            messagebox.showerror("获取控件失败", "无法获取微信窗口控件。请确保窗口可见、未被其他窗口遮挡，并且焦点正确。")
            app_instance.analysis_done()
            return
        
        moment = MomentData()
        
        app_instance.update_status_text("\n正在收集朋友圈数据 (这可能需要一些时间)...\n")
        # Wrap data collection in a way that it can be interrupted or provide progress
        collect_moment_data(wechat_window_control, moment) # Pass the uiautomation control
        
        if not moment.author and not moment.content and not moment.likes:
             app_instance.update_status_text("未能收集到有效的朋友圈数据。请检查朋友圈页面是否已加载内容。\n")
             messagebox.showwarning("无数据", "未能收集到有效的朋友圈数据。请检查朋友圈页面是否已加载内容，或尝试滚动页面后重试。")
             app_instance.analysis_done()
             return

        excel_file = "朋友圈数据.xlsx"
        app_instance.update_status_text(f"\n正在保存数据到 {excel_file}...\n")
        excel_path = save_to_excel(moment, excel_file)
        app_instance.update_status_text(f"数据已保存到: {excel_path}\n")
        app_instance.set_excel_path(excel_path)
        
        report_file_name = "朋友圈分析报告.txt"
        app_instance.update_status_text(f"\n正在生成分析报告...\n")
        report_text_content = generate_report_text(moment) # Get text first
        report_file_path = save_report_to_file(report_text_content, report_file_name) # Then save
        app_instance.update_status_text(f"分析报告已保存到: {report_file_path}\n")
        app_instance.set_report_path(report_file_path)
        
        app_instance.update_status_text("\n" + "="*30 + "\n朋友圈分析报告预览:\n" + "="*30 + "\n")
        app_instance.display_report_preview(report_text_content)
        
        app_instance.update_status_text("\n分析完成！\n")
        messagebox.showinfo("完成", "朋友圈数据分析和报告生成已完成！")
        
    except Exception as e:
        error_msg = f"程序执行出错: {str(e)}\n请查看控制台输出获取更详细的错误信息。"
        app_instance.update_status_text(error_msg + "\n")
        messagebox.showerror("执行错误", error_msg)
        import traceback
        traceback.print_exc() # Print full traceback to console for debugging
    finally:
        app_instance.analysis_done()

class MomentAnalysisApp:
    def __init__(self, root):
        self.root = root
        self.root.title("微信工具集")
        self.root.geometry("700x600")

        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'favicon.ico')
        if os.path.exists(icon_path):
            self.root.iconbitmap(icon_path)

        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 创建标签页控件
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建朋友圈分析标签页
        self.moments_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.moments_frame, text="朋友圈分析")

        # 创建群邀请标签页
        self.invite_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.invite_frame, text="群邀请工具")

        # 初始化朋友圈分析页面
        self.init_moments_page()

        # 初始化群邀请页面
        self.init_invite_page()

        # 重定向stdout
        self.stdout_redirector = StdoutRedirector(self.status_text)
        sys.stdout = self.stdout_redirector
        sys.stderr = self.stdout_redirector

        self.analysis_thread = None
        self.inviting = False
        self.invite_paused = False  # 添加暂停状态
        self.invite_thread = None   # 添加邀请线程引用
        self.hotkey_check_timer = None  # 添加快捷键检查定时器
        self.last_ctrl_1_time = 0  # 记录上次按键时间，防止重复触发
        self.global_hotkey_registered = False  # 全局热键注册状态

    def init_moments_page(self):
        """初始化朋友圈分析页面"""
        # Frame for controls
        self.control_frame = ttk.Frame(self.moments_frame, padding="10")
        self.control_frame.pack(fill=tk.X, pady=5)

        self.start_button = ttk.Button(self.control_frame, text="开始分析朋友圈", command=self.start_analysis_thread)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.progress_bar = ttk.Progressbar(self.control_frame, orient="horizontal", length=200, mode="indeterminate")
        self.progress_bar.pack(side=tk.LEFT, padx=10, pady=5, fill=tk.X, expand=True)

        # Frame for status and report
        self.status_frame = ttk.LabelFrame(self.moments_frame, text="状态与报告预览", padding="10")
        self.status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.status_text = scrolledtext.ScrolledText(self.status_frame, wrap=tk.WORD, height=15, font=("微软雅黑", 9))
        self.status_text.pack(fill=tk.BOTH, expand=True)
        self.status_text.configure(state='disabled')

        # Frame for file paths
        self.path_frame = ttk.LabelFrame(self.moments_frame, text="生成文件路径", padding="10")
        self.path_frame.pack(fill=tk.X, padx=10, pady=5)

        self.excel_path_label = ttk.Label(self.path_frame, text="Excel文件: 未生成")
        self.excel_path_label.pack(anchor=tk.W, padx=5)
        self.report_path_label = ttk.Label(self.path_frame, text="报告文件: 未生成")
        self.report_path_label.pack(anchor=tk.W, padx=5)

    def init_invite_page(self):
        """初始化群邀请页面"""
        # 说明标签
        instruction_text = """使用说明：
1. 将光标放在要输入昵称的位置
2. 在下方文本框中输入要邀请的用户昵称（一行一个）
3. 点击"开始"按钮
4. 程序会自动输入昵称并发送
5. 按 Ctrl+1 可以快速停止操作"""

        self.instruction_label = ttk.Label(self.invite_frame, text=instruction_text, justify=tk.LEFT)
        self.instruction_label.pack(pady=10, padx=10, anchor=tk.W)

        # 文本框用于输入昵称
        self.invite_text_area = scrolledtext.ScrolledText(self.invite_frame, width=40, height=10)
        self.invite_text_area.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

        # 按钮框架
        button_frame = ttk.Frame(self.invite_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        # 开始按钮
        self.invite_start_button = ttk.Button(button_frame, text="开始", command=self.start_invite)
        self.invite_start_button.pack(side=tk.LEFT, padx=5)

        # 暂停/继续按钮
        self.invite_pause_button = ttk.Button(button_frame, text="暂停", command=self.toggle_pause_invite)
        self.invite_pause_button.pack(side=tk.LEFT, padx=5)
        self.invite_pause_button.config(state=tk.DISABLED)

        # 停止按钮
        self.invite_stop_button = ttk.Button(button_frame, text="停止", command=self.stop_invite)
        self.invite_stop_button.pack(side=tk.LEFT, padx=5)
        self.invite_stop_button.config(state=tk.DISABLED)

        # 清空按钮
        self.invite_clear_button = ttk.Button(button_frame, text="清空", command=self.clear_invite_text)
        self.invite_clear_button.pack(side=tk.LEFT, padx=5)

        # 状态标签
        self.invite_status_label = ttk.Label(self.invite_frame, text="")
        self.invite_status_label.pack(pady=5)

        # 设置全局快捷键
        self.setup_hotkeys()

    def setup_hotkeys(self):
        """设置全局快捷键"""
        try:
            # 绑定多种可能的快捷键格式
            self.root.bind_all('<Control-Key-1>', self.hotkey_stop_invite)
            self.root.bind_all('<Control-1>', self.hotkey_stop_invite)
            self.root.bind_all('<Control-KeyPress-1>', self.hotkey_stop_invite)

            # 确保窗口可以接收焦点
            self.root.focus_set()
            print("快捷键已设置: Ctrl+1")
        except Exception as e:
            print(f"设置快捷键失败: {e}")

    def start_hotkey_monitor(self):
        """启动快捷键监控"""
        # 启动全局快捷键监控线程
        if hasattr(self, 'inviting') and self.inviting:
            self.hotkey_monitor_thread = threading.Thread(target=self.global_hotkey_monitor, daemon=True)
            self.hotkey_monitor_thread.start()
            print("全局快捷键监控已启动")

    def global_hotkey_monitor(self):
        """全局快捷键监控线程"""
        print("开始监控全局快捷键 Ctrl+1...")

        # 记录按键状态
        ctrl_was_pressed = False
        key_1_was_pressed = False

        while hasattr(self, 'inviting') and self.inviting:
            try:
                # 检查按键状态
                ctrl_pressed = win32api.GetAsyncKeyState(0x11) & 0x8000  # VK_CONTROL
                key_1_pressed = win32api.GetAsyncKeyState(0x31) & 0x8000  # VK_1

                # 检测按键按下事件（从未按下到按下）
                if ctrl_pressed and key_1_pressed and not (ctrl_was_pressed and key_1_was_pressed):
                    current_time = time.time()
                    # 防止重复触发
                    if current_time - self.last_ctrl_1_time > 0.5:
                        self.last_ctrl_1_time = current_time
                        print("全局快捷键 Ctrl+1 被按下")
                        # 在主线程中执行停止操作
                        self.root.after(0, self.hotkey_stop_invite)

                # 更新按键状态
                ctrl_was_pressed = bool(ctrl_pressed)
                key_1_was_pressed = bool(key_1_pressed)

                # 短暂休眠，避免过度占用CPU
                time.sleep(0.05)  # 50ms检查一次

            except Exception as e:
                print(f"全局快捷键监控出错: {e}")
                time.sleep(0.1)

        print("全局快捷键监控已停止")

    def hotkey_stop_invite(self, event=None):
        """快捷键停止邀请"""
        print("检测到 Ctrl+1 快捷键")
        try:
            if hasattr(self, 'inviting') and self.inviting:
                print("正在停止群邀请...")
                self.stop_invite()
                # 使用after方法确保在主线程中显示消息框
                self.root.after(0, lambda: messagebox.showinfo("快捷键", "已通过 Ctrl+1 停止群邀请操作"))
            else:
                print("当前没有正在进行的群邀请操作")
        except Exception as e:
            print(f"处理快捷键时出错: {e}")
        return "break"  # 阻止事件继续传播

    def clear_invite_text(self):
        """清空群邀请文本框"""
        self.invite_text_area.delete(1.0, tk.END)
        self.invite_status_label.config(text="")

    def toggle_pause_invite(self):
        """切换暂停/继续状态"""
        if not self.inviting:
            return

        self.invite_paused = not self.invite_paused
        if self.invite_paused:
            self.invite_pause_button.config(text="继续")
            self.invite_status_label.config(text="已暂停 - 点击继续按钮或按 Ctrl+1 停止")
        else:
            self.invite_pause_button.config(text="暂停")
            self.invite_status_label.config(text="继续处理中...")

    def stop_invite(self):
        """停止群邀请"""
        if hasattr(self, 'inviting') and self.inviting:
            print("正在停止群邀请...")
            self.inviting = False
            self.invite_paused = False
            self.invite_status_label.config(text="已停止")

            # 停止快捷键监控（全局监控线程会自动停止）
            if hasattr(self, 'hotkey_check_timer') and self.hotkey_check_timer:
                self.root.after_cancel(self.hotkey_check_timer)
                self.hotkey_check_timer = None

            self.reset_invite_buttons()
            print("群邀请已停止")

    def reset_invite_buttons(self):
        """重置邀请按钮状态"""
        self.invite_start_button.config(state=tk.NORMAL)
        self.invite_pause_button.config(state=tk.DISABLED, text="暂停")
        self.invite_stop_button.config(state=tk.DISABLED)

    def start_invite(self):
        """开始群邀请过程"""
        if self.inviting:
            messagebox.showwarning("提示", "正在处理中...")
            return

        nicknames = self.invite_text_area.get(1.0, tk.END).strip().split('\n')
        nicknames = [nick.strip() for nick in nicknames if nick.strip()]

        if not nicknames:
            messagebox.showwarning("提示", "请先输入要邀请的用户昵称！")
            return

        # 确认开始
        if not messagebox.askyesno("确认", "请确保已打开添加成员窗口。\n是否开始？"):
            return

        self.inviting = True
        self.invite_paused = False

        # 更新按钮状态
        self.invite_start_button.config(state=tk.DISABLED)
        self.invite_pause_button.config(state=tk.NORMAL)
        self.invite_stop_button.config(state=tk.NORMAL)

        # 启动快捷键监控
        self.start_hotkey_monitor()

        # 启动邀请线程
        self.invite_thread = threading.Thread(target=self.invite_users, args=(nicknames,), daemon=True)
        self.invite_thread.start()

    def invite_users(self, nicknames):
        """执行群邀请过程"""
        try:
            # 查找添加成员窗口
            window = find_add_member_window()
            if not window:
                self.root.after(0, lambda: messagebox.showerror("错误", "未找到添加成员窗口，请确保窗口已打开"))
                return

            # 激活窗口
            try:
                window.SetFocus()
                time.sleep(0.5)

                total = len(nicknames)
                for i, nickname in enumerate(nicknames, 1):
                    # 检查是否应该停止
                    if not self.inviting:
                        self.root.after(0, lambda: self.invite_status_label.config(text="已停止"))
                        break

                    # 检查是否暂停
                    while self.invite_paused and self.inviting:
                        time.sleep(0.1)  # 暂停时等待
                        if not self.inviting:  # 在暂停期间可能被停止
                            break

                    if not self.inviting:  # 再次检查是否停止
                        break

                    # 更新状态
                    self.root.after(0, lambda n=nickname, idx=i, tot=total:
                                  self.invite_status_label.config(text=f"正在处理: {n} ({idx}/{tot})"))

                    # 输入昵称并回车
                    pyperclip.copy(nickname)
                    time.sleep(0.1)
                    pyautogui.hotkey('ctrl', 'v')
                    time.sleep(0.2)
                    pyautogui.press('enter')
                    time.sleep(0.3)

                if self.inviting:  # 只有在正常完成时才显示完成消息
                    self.root.after(0, lambda: self.invite_status_label.config(text="处理完成！"))
                    self.root.after(0, lambda: messagebox.showinfo("完成", "处理完成！"))

            except Exception as e:
                print(f"操作窗口时出错: {str(e)}")
                self.root.after(0, lambda: messagebox.showerror("错误", f"操作窗口时出错：\n{str(e)}"))

        except Exception as e:
            self.root.after(0, lambda: self.invite_status_label.config(text=f"发生错误: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中发生错误：\n{str(e)}"))

        finally:
            print("清理群邀请资源...")
            self.inviting = False
            self.invite_paused = False

            # 停止快捷键监控（全局监控线程会自动停止）
            if hasattr(self, 'hotkey_check_timer') and self.hotkey_check_timer:
                self.root.after_cancel(self.hotkey_check_timer)
                self.hotkey_check_timer = None

            self.root.after(0, self.reset_invite_buttons)
            print("群邀请资源清理完成")

    def start_analysis_thread(self):
        if self.analysis_thread and self.analysis_thread.is_alive():
            messagebox.showwarning("正在运行", "分析已经在运行中！")
            return
        
        self.clear_previous_results()
        self.start_button.config(state=tk.DISABLED)
        self.progress_bar.start(10)
        
        self.analysis_thread = threading.Thread(target=main_logic, args=(self,), daemon=True)
        self.analysis_thread.start()

    def clear_previous_results(self):
        self.status_text.configure(state='normal')
        self.status_text.delete(1.0, tk.END)
        self.status_text.configure(state='disabled')
        self.excel_path_label.config(text="Excel文件: 未生成")
        self.report_path_label.config(text="报告文件: 未生成")

    def update_status_text(self, message):
        self.status_text.configure(state='normal')
        self.status_text.insert(tk.END, message)
        self.status_text.see(tk.END)
        self.status_text.configure(state='disabled')
        self.root.update_idletasks()

    def display_report_preview(self, report_content):
        self.update_status_text("\n--- 报告预览 ---\n")
        self.update_status_text(report_content)
        self.update_status_text("\n--- 报告预览结束 ---\n")
    
    def set_excel_path(self, path):
        self.excel_path_label.config(text=f"Excel文件: {path}")

    def set_report_path(self, path):
        self.report_path_label.config(text=f"报告文件: {path}")

    def analysis_done(self):
        self.start_button.config(state=tk.NORMAL)
        self.progress_bar.stop()
        self.root.update_idletasks()

def find_add_member_window():
    """查找添加成员窗口"""
    result = []
    
    def enum_windows_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            if text and class_name == 'AddMemberWnd':
                windows.append(hwnd)
    
    try:
        # 枚举所有顶级窗口
        win32gui.EnumWindows(enum_windows_callback, result)
        
        if result:
            hwnd = result[0]
            print(f"找到添加成员窗口，句柄: {hwnd}")
            # 将win32gui窗口句柄转换为UIAutomation控件
            add_member = auto.ControlFromHandle(hwnd)
            if add_member and add_member.Exists():
                print("成功获取到窗口控件")
                return add_member
            else:
                print("无法获取窗口控件")
                return None
        
        print("未找到添加成员窗口")
        return None
    except Exception as e:
        print(f"查找添加成员窗口时出错: {str(e)}")
        return None

class LoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("微信朋友圈打印")
        self.root.geometry("500x400")
        
        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'favicon.ico')
        if os.path.exists(icon_path):
            self.root.iconbitmap(icon_path)
        
        # 创建主框架并添加内边距
        main_frame = ttk.Frame(self.root, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="登录验证", font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 机器码显示
        self.machine_code = MachineCodeGenerator().getMachineCode()
        ttk.Label(main_frame, text="机器码：", font=("微软雅黑", 10)).pack(pady=(10, 5))
        
        # 机器码框架
        code_frame = ttk.Frame(main_frame)
        code_frame.pack(fill=tk.X, pady=5)
        
        self.code_label = ttk.Label(code_frame, text=self.machine_code, font=("Consolas", 12))
        self.code_label.pack(side=tk.LEFT, padx=5)
        
        copy_btn = ttk.Button(code_frame, text="复制", command=self.copy_code, width=10)
        copy_btn.pack(side=tk.RIGHT, padx=5)
        
        # 状态显示
        self.status_label = ttk.Label(main_frame, text="正在验证授权...", 
                                    wraplength=400, font=("微软雅黑", 10))
        self.status_label.pack(pady=30)
        
        # 联系信息
        contact_frame = ttk.LabelFrame(main_frame, text="联系信息", padding="15")
        contact_frame.pack(fill=tk.X, pady=(10, 20))
        ttk.Label(contact_frame, text="如需授权，请联系：18129973837",
                 font=("微软雅黑", 10)).pack(pady=5)
        
        # 验证按钮
        self.verify_btn = ttk.Button(main_frame, text="重新验证", 
                                   command=self.verify_auth, width=15)
        self.verify_btn.pack(pady=10)
        
        # 设置窗口最小大小
        self.root.minsize(450, 350)
        
        # 居中显示窗口
        self.center_window()
        
        # 开始验证
        self.verify_auth()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def copy_code(self):
        """复制机器码到剪贴板"""
        pyperclip.copy(self.machine_code)
        messagebox.showinfo("提示", "机器码已复制到剪贴板！")
    
    def verify_auth(self):
        """验证授权"""
        self.verify_btn.config(state=tk.DISABLED)
        self.status_label.config(text="正在验证授权...")
        threading.Thread(target=self._verify_auth_thread, daemon=True).start()
    
    def _verify_auth_thread(self):
        """验证授权的线程"""
        try:
            # 检查当前日期是否在2025年10月1日之前
            import datetime
            deadline = datetime.datetime(2025, 10, 1)
            now = datetime.datetime.now()
            
            if now < deadline:
                # 2025年7月1日之前自动通过验证
                self.root.after(100, self.start_main_app)
                return True
                
            # 2025年7月1日之后需要在线验证
            register = YouDaoNote(client_config["request_url"])
            endTime = register.getAuthInfo(self.machine_code)
            
            # 先判断特殊状态
            if endTime == -1:
                # 永久授权直接进入
                self.root.after(100, self.start_main_app)
                return True
            elif endTime == -2:
                self.status_label.config(text="您的设备授权已过期，暂无法使用！\n请联系客服续期。")
                self.verify_btn.config(state=tk.NORMAL)
                return False
            elif endTime == 0:
                self.status_label.config(text="您的设备没有授权，暂无法使用！\n请联系客服获取授权。")
                self.verify_btn.config(state=tk.NORMAL)
                return False
            elif endTime > 0:
                # 检查临时授权是否过期
                import time
                current_time = int(time.time())
                if current_time > endTime:
                    self.status_label.config(text="您的设备授权已过期，暂无法使用！\n请联系客服续期。")
                    self.verify_btn.config(state=tk.NORMAL)
                    return False
                # 临时授权有效，直接进入
                self.root.after(100, self.start_main_app)
                return True
            else:
                # 处理意外的返回值
                self.status_label.config(text="验证返回未知状态，请联系客服处理。")
                self.verify_btn.config(state=tk.NORMAL)
                return False
        except Exception as e:
            self.status_label.config(text=f"验证出错：{str(e)}\n请检查网络连接后重试。")
            self.verify_btn.config(state=tk.NORMAL)
            return False
    
    def start_main_app(self):
        """启动主应用"""
        self.root.destroy()
        root = tk.Tk()
        app = MomentAnalysisApp(root)
        root.mainloop()
    
    def run(self):
        """运行登录窗口"""
        self.root.mainloop()

if __name__ == "__main__":
    # 检查当前日期是否在2025年7月1日之前
    import datetime
    deadline = datetime.datetime(2025, 7, 1)
    now = datetime.datetime.now()
    
    if now < deadline:
        # 2025年7月1日之前直接启动主程序
        root = tk.Tk()
        app = MomentAnalysisApp(root)
        root.mainloop()
    else:
        # 2025年7月1日后需要验证
        login = LoginWindow()
        login.run()