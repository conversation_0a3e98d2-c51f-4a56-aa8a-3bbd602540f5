#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试快捷键功能的简单脚本
"""

import tkinter as tk
from tkinter import messagebox
import win32api
import time
import threading

class HotkeyTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("快捷键测试")
        self.root.geometry("400x300")
        
        self.running = False
        self.last_ctrl_1_time = 0
        self.hotkey_check_timer = None
        
        # 创建界面
        self.setup_ui()
        
        # 设置快捷键
        self.setup_hotkeys()
    
    def setup_ui(self):
        """设置用户界面"""
        tk.Label(self.root, text="快捷键测试程序", font=("微软雅黑", 16)).pack(pady=20)
        
        tk.Label(self.root, text="按 Ctrl+1 测试快捷键功能", font=("微软雅黑", 12)).pack(pady=10)
        
        self.start_button = tk.But<PERSON>(self.root, text="开始测试", command=self.start_test, font=("微软雅黑", 12))
        self.start_button.pack(pady=10)
        
        self.stop_button = tk.Button(self.root, text="停止测试", command=self.stop_test, font=("微软雅黑", 12))
        self.stop_button.pack(pady=10)
        self.stop_button.config(state=tk.DISABLED)
        
        self.status_label = tk.Label(self.root, text="状态: 未开始", font=("微软雅黑", 10))
        self.status_label.pack(pady=20)
    
    def setup_hotkeys(self):
        """设置快捷键"""
        try:
            # 绑定tkinter快捷键
            self.root.bind_all('<Control-Key-1>', self.hotkey_triggered)
            self.root.bind_all('<Control-1>', self.hotkey_triggered)
            
            self.root.focus_set()
            print("快捷键已设置: Ctrl+1")
        except Exception as e:
            print(f"设置快捷键失败: {e}")
    
    def start_test(self):
        """开始测试"""
        self.running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_label.config(text="状态: 测试中 - 请按 Ctrl+1")
        
        # 启动快捷键监控
        self.start_hotkey_monitor()
        
        # 启动模拟工作线程
        threading.Thread(target=self.simulate_work, daemon=True).start()
    
    def stop_test(self):
        """停止测试"""
        self.running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="状态: 已停止")
        
        # 停止快捷键监控
        if self.hotkey_check_timer:
            self.root.after_cancel(self.hotkey_check_timer)
            self.hotkey_check_timer = None
    
    def start_hotkey_monitor(self):
        """启动快捷键监控"""
        self.check_hotkey()
    
    def check_hotkey(self):
        """检查快捷键状态"""
        try:
            # 检查Ctrl+1是否被按下
            ctrl_pressed = win32api.GetAsyncKeyState(0x11) & 0x8000  # VK_CONTROL
            key_1_pressed = win32api.GetAsyncKeyState(0x31) & 0x8000  # VK_1
            
            current_time = time.time()
            
            if ctrl_pressed and key_1_pressed:
                # 防止重复触发（500ms内只触发一次）
                if current_time - self.last_ctrl_1_time > 0.5:
                    self.last_ctrl_1_time = current_time
                    self.hotkey_triggered()
            
            # 继续监控（每100ms检查一次）
            if self.running:
                self.hotkey_check_timer = self.root.after(100, self.check_hotkey)
        except Exception as e:
            print(f"检查快捷键时出错: {e}")
            # 如果出错，继续监控
            if self.running:
                self.hotkey_check_timer = self.root.after(100, self.check_hotkey)
    
    def hotkey_triggered(self, event=None):
        """快捷键被触发"""
        print("检测到 Ctrl+1 快捷键")
        if self.running:
            self.stop_test()
            messagebox.showinfo("快捷键", "Ctrl+1 快捷键工作正常！\n已停止测试。")
        return "break"
    
    def simulate_work(self):
        """模拟工作过程"""
        count = 0
        while self.running:
            count += 1
            self.root.after(0, lambda c=count: self.status_label.config(text=f"状态: 测试中 ({c}s) - 请按 Ctrl+1"))
            time.sleep(1)
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = HotkeyTest()
    app.run()
