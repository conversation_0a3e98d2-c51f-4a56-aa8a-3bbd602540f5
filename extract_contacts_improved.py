import uiautomation as auto
import time
from datetime import datetime
import pandas as pd
import os
import json
import sys

# 尝试导入tabulate，如果失败则使用简单的表格显示
try:
    from tabulate import tabulate
    TABULATE_AVAILABLE = True
except ImportError:
    TABULATE_AVAILABLE = False
    print("警告: tabulate 未安装，将使用简单的表格显示")

# 尝试导入win32api，如果失败则提供替代方案
try:
    import win32api
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("警告: win32api 未安装，将使用替代滚动方法")

def save_control_structure(window, output_file="window_structure.json"):
    """保存窗口控件结构到JSON文件"""
    def get_control_info(control):
        return {
            "ControlType": control.ControlType,
            "ControlTypeName": control.ControlTypeName,
            "Name": control.Name,
            "ClassName": control.ClassName,
            "AutomationId": control.AutomationId,
            "BoundingRectangle": {
                "left": control.BoundingRectangle.left,
                "top": control.BoundingRectangle.top,
                "right": control.BoundingRectangle.right,
                "bottom": control.BoundingRectangle.bottom,
                "width": control.BoundingRectangle.width(),
                "height": control.BoundingRectangle.height()
            },
            "IsEnabled": control.IsEnabled,
            "IsOffscreen": control.IsOffscreen,
            "Children": []
        }
    
    def build_control_tree(control):
        control_info = get_control_info(control)
        for child in control.GetChildren():
            child_info = build_control_tree(child)
            control_info["Children"].append(child_info)
        return control_info
    
    print(f"正在保存窗口控件结构到 {output_file}...")
    control_tree = build_control_tree(window)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(control_tree, f, ensure_ascii=False, indent=2)
    print(f"窗口控件结构已保存到 {output_file}")

def print_control_info(control, depth=0, max_depth=None):
    """递归打印控件信息"""
    if max_depth is not None and depth > max_depth:
        return
    
    indent = "  " * depth
    print(f"{indent}控件: {control.ControlTypeName}")
    print(f"{indent}名称: {control.Name}")
    print(f"{indent}类名: {control.ClassName}")
    print(f"{indent}位置: {control.BoundingRectangle}")
    print(f"{indent}可见: {not control.IsOffscreen}")
    
    children = control.GetChildren()
    print(f"{indent}子控件数: {len(children)}")
    
    if children:
        print(f"{indent}子控件:")
        for child in children:
            print_control_info(child, depth + 1, max_depth)

def find_lists_recursive(control, depth=0, max_depth=10):
    """递归查找所有列表控件"""
    if depth > max_depth:
        return []
    
    lists = []
    indent = "  " * depth
    
    # 检查当前控件是否是列表
    if control.ControlType == auto.ControlType.ListControl:
        print(f"{indent}找到列表控件:")
        print(f"{indent}  位置: {control.BoundingRectangle}")
        print(f"{indent}  名称: {control.Name}")
        lists.append(control)
    
    # 递归检查子控件
    for child in control.GetChildren():
        child_lists = find_lists_recursive(child, depth + 1, max_depth)
        lists.extend(child_lists)
    
    return lists

def find_contact_list(window):
    """查找联系人列表控件"""
    print("\n=== 查找联系人列表 ===")

    # 根据JSON结构分析，主要联系人列表应该在右侧区域
    # 位置大约在 (2349,79,2948,1080)，宽度599，高度1001

    # 递归查找所有列表控件
    print("开始递归查找列表控件...")
    lists = find_lists_recursive(window)

    if not lists:
        print("未找到任何列表控件")
        return None

    print(f"\n找到 {len(lists)} 个列表控件")

    # 分析每个列表控件，寻找主要的联系人列表
    main_contact_list = None
    max_width = 0

    for i, lst in enumerate(lists, 1):
        print(f"\n分析列表 {i}:")
        rect = lst.BoundingRectangle
        print(f"位置: {rect}")
        print(f"宽度: {rect.width()}, 高度: {rect.height()}")

        # 获取列表项
        items = lst.GetChildren()
        print(f"列表项数量: {len(items)}")

        # 检查第一个列表项（如果存在）
        if items:
            first_item = items[0]
            print(f"第一个列表项:")
            print(f"  类型: {first_item.ControlTypeName}")
            print(f"  名称: {first_item.Name}")
            print(f"  位置: {first_item.BoundingRectangle}")

            # 根据宽度和高度判断是否为主要联系人列表
            # 主要联系人列表应该是最宽的列表，且高度较大
            if (first_item.ControlType == auto.ControlType.ListItemControl and
                rect.width() > max_width and rect.width() > 500):
                print("这看起来是主要联系人列表")
                main_contact_list = lst
                max_width = rect.width()

    if main_contact_list:
        print(f"\n选择主要联系人列表，宽度: {max_width}")
        return main_contact_list
    else:
        print("未找到合适的联系人列表")
        return None

def extract_contact_info(list_item):
    """从列表项中提取联系人信息"""
    try:
        # 初始化联系人信息字典
        contact = {
            "昵称": "",
            "备注": "",
            "标签": "",
            "微信号": "",
            "位置": str(list_item.BoundingRectangle)
        }

        print(f"\n分析列表项:")
        print(f"类型: {list_item.ControlTypeName}")
        print(f"名称: {list_item.Name}")
        print(f"位置: {list_item.BoundingRectangle}")

        # 根据JSON结构分析，每个联系人项的结构：
        # - CheckBox（复选框）
        # - 第一个区域：头像按钮（包含完整昵称）+ 显示文本（可能是备注）
        # - 第二个区域：备注按钮
        # - 其他区域：操作按钮

        buttons = []
        texts = []

        def collect_controls(control, depth=0):
            """收集所有按钮和文本控件"""
            indent = "  " * depth

            if control.ControlType == auto.ControlType.ButtonControl and control.Name:
                print(f"{indent}找到按钮: '{control.Name}' 位置: {control.BoundingRectangle}")
                buttons.append({
                    "name": control.Name,
                    "rect": control.BoundingRectangle,
                    "control": control
                })

            elif control.ControlType == auto.ControlType.TextControl and control.Name:
                print(f"{indent}找到文本: '{control.Name}' 位置: {control.BoundingRectangle}")
                texts.append({
                    "name": control.Name,
                    "rect": control.BoundingRectangle,
                    "control": control
                })

            for child in control.GetChildren():
                collect_controls(child, depth + 1)

        collect_controls(list_item)

        # 分析收集到的控件
        print(f"找到 {len(buttons)} 个按钮，{len(texts)} 个文本")

        # 按钮通常包含昵称信息，选择最左边的按钮作为主要昵称
        if buttons:
            # 按X坐标排序，选择最左边的按钮
            buttons.sort(key=lambda x: x["rect"].left)
            main_button = buttons[0]
            contact["昵称"] = main_button["name"]
            print(f"主要昵称（来自按钮）: {contact['昵称']}")

            # 如果有多个按钮，第二个可能是备注
            if len(buttons) > 1:
                second_button = buttons[1]
                # 如果第二个按钮的名称与第一个不同，可能是备注
                if second_button["name"] != main_button["name"]:
                    contact["备注"] = second_button["name"]
                    print(f"备注（来自第二个按钮）: {contact['备注']}")

        # 文本控件可能包含显示名称或其他信息
        if texts:
            # 按X坐标排序
            texts.sort(key=lambda x: x["rect"].left)
            main_text = texts[0]

            # 如果文本与昵称不同，可能是显示名称或备注
            if main_text["name"] != contact["昵称"] and main_text["name"].strip():
                if not contact["备注"]:
                    contact["备注"] = main_text["name"]
                    print(f"备注（来自文本）: {contact['备注']}")
                else:
                    contact["标签"] = main_text["name"]
                    print(f"标签（来自文本）: {contact['标签']}")

        # 清理空值
        for key in contact:
            if isinstance(contact[key], str):
                contact[key] = contact[key].strip()

        print(f"最终提取结果: {contact}")
        return contact

    except Exception as e:
        print(f"提取联系人信息时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def scroll_and_extract(contact_list):
    """滚动列表并提取所有联系人信息"""
    contacts = []
    processed_positions = set()  # 使用位置来避免重复
    scroll_count = 0
    max_scroll = 200  # 增加最大滚动次数
    consecutive_no_new = 0  # 连续没有新联系人的次数
    max_consecutive_no_new = 5  # 最大连续无新联系人次数

    print("\n=== 开始滚动提取联系人 ===")

    while scroll_count < max_scroll and consecutive_no_new < max_consecutive_no_new:
        print(f"\n当前滚动次数: {scroll_count + 1}")

        # 获取当前可见的列表项
        list_items = contact_list.GetChildren()
        print(f"当前可见列表项数量: {len(list_items)}")

        if not list_items:
            print("未找到列表项，停止滚动")
            break

        # 提取当前可见项的信息
        new_contacts_in_this_scroll = 0

        for i, item in enumerate(list_items):
            if item.ControlType == auto.ControlType.ListItemControl:
                # 使用位置作为唯一标识
                item_position = f"{item.BoundingRectangle.left},{item.BoundingRectangle.top}"

                if item_position not in processed_positions:
                    print(f"\n处理第 {i+1} 个列表项...")
                    contact = extract_contact_info(item)

                    if contact and contact["昵称"]:  # 确保至少有昵称
                        contacts.append(contact)
                        processed_positions.add(item_position)
                        new_contacts_in_this_scroll += 1
                        print(f"✓ 添加新联系人: {contact['昵称']}")
                    else:
                        print("× 未能提取有效联系人信息")
                        processed_positions.add(item_position)  # 标记为已处理，避免重复尝试
                else:
                    print(f"跳过已处理的联系人（位置: {item_position}）")

        print(f"本次滚动新增联系人: {new_contacts_in_this_scroll}")

        if new_contacts_in_this_scroll == 0:
            consecutive_no_new += 1
            print(f"连续 {consecutive_no_new} 次滚动无新联系人")
        else:
            consecutive_no_new = 0

        # 尝试滚动到下一页
        if list_items:
            try:
                print("尝试滚动到下一页...")
                # 滚动到最后一个项目
                last_item = list_items[-1]

                # 尝试多种滚动方式
                try:
                    last_item.ScrollIntoView()
                except:
                    # 如果ScrollIntoView失败，尝试使用键盘滚动
                    contact_list.SetFocus()
                    time.sleep(0.2)
                    # 发送Page Down键
                    if WIN32_AVAILABLE:
                        win32api.keybd_event(0x22, 0, 0, 0)  # Page Down key down
                        win32api.keybd_event(0x22, 0, 2, 0)  # Page Down key up
                    else:
                        # 使用uiautomation的键盘输入
                        auto.SendKeys('{PGDN}')

                time.sleep(1)  # 增加等待时间，确保滚动完成
                scroll_count += 1

            except Exception as e:
                print(f"滚动时出错: {e}")
                # 尝试备用滚动方法
                try:
                    contact_list.SetFocus()
                    time.sleep(0.2)
                    # 使用方向键向下滚动
                    if WIN32_AVAILABLE:
                        for _ in range(5):
                            win32api.keybd_event(0x28, 0, 0, 0)  # Down arrow key down
                            win32api.keybd_event(0x28, 0, 2, 0)  # Down arrow key up
                            time.sleep(0.1)
                    else:
                        # 使用uiautomation的键盘输入
                        for _ in range(5):
                            auto.SendKeys('{DOWN}')
                            time.sleep(0.1)
                    time.sleep(0.5)
                    scroll_count += 1
                except:
                    print("所有滚动方法都失败，停止滚动")
                    break
        else:
            print("没有列表项可滚动，停止")
            break

    print(f"\n=== 提取完成 ===")
    print(f"总滚动次数: {scroll_count}")
    print(f"总共提取到 {len(contacts)} 个联系人")
    return contacts

def print_summary(contacts):
    """打印联系人信息摘要"""
    print("\n=== 提取的联系人信息摘要 ===")

    if not contacts:
        print("没有提取到任何联系人信息")
        return

    # 准备表格数据
    table_data = []
    for i, contact in enumerate(contacts, 1):
        table_data.append([
            i,
            contact["昵称"][:20] + "..." if len(contact["昵称"]) > 20 else contact["昵称"],
            contact["备注"][:15] + "..." if contact["备注"] and len(contact["备注"]) > 15 else (contact["备注"] if contact["备注"] else "-"),
            contact["标签"][:15] + "..." if contact["标签"] and len(contact["标签"]) > 15 else (contact["标签"] if contact["标签"] else "-"),
            contact["微信号"][:15] + "..." if contact["微信号"] and len(contact["微信号"]) > 15 else (contact["微信号"] if contact["微信号"] else "-")
        ])

    # 使用tabulate打印表格（如果可用），否则使用简单格式
    if TABULATE_AVAILABLE:
        print(tabulate(
            table_data,
            headers=["序号", "昵称", "备注", "标签", "微信号"],
            tablefmt="grid",
            maxcolwidths=[4, 20, 15, 15, 15]
        ))
    else:
        # 简单的表格显示
        print(f"{'序号':<4} {'昵称':<20} {'备注':<15} {'标签':<15} {'微信号':<15}")
        print("-" * 75)
        for row in table_data:
            print(f"{row[0]:<4} {row[1]:<20} {row[2]:<15} {row[3]:<15} {row[4]:<15}")

    # 统计信息
    print(f"\n=== 统计信息 ===")
    print(f"总计联系人: {len(contacts)} 个")

    # 统计有备注的联系人
    with_remark = sum(1 for c in contacts if c["备注"])
    print(f"有备注的联系人: {with_remark} 个 ({with_remark/len(contacts)*100:.1f}%)")

    # 统计有标签的联系人
    with_tag = sum(1 for c in contacts if c["标签"])
    print(f"有标签的联系人: {with_tag} 个 ({with_tag/len(contacts)*100:.1f}%)")

    # 统计有微信号的联系人
    with_wechat_id = sum(1 for c in contacts if c["微信号"])
    print(f"有微信号的联系人: {with_wechat_id} 个 ({with_wechat_id/len(contacts)*100:.1f}%)")

def wait_for_window_ready(window, timeout=10):
    """等待窗口完全加载"""
    print("等待窗口完全加载...")
    start_time = time.time()

    while time.time() - start_time < timeout:
        try:
            # 检查窗口是否可见且有子控件
            if window.IsEnabled and not window.IsOffscreen:
                children = window.GetChildren()
                if len(children) > 0:
                    print("窗口已准备就绪")
                    return True
        except:
            pass
        time.sleep(0.5)

    print("等待窗口超时")
    return False

def main():
    print("\n=== 通讯录管理窗口联系人提取 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 查找通讯录管理窗口
    window_title = "通讯录管理"
    print(f"正在查找窗口: {window_title}")

    window = auto.WindowControl(searchDepth=1, Name=window_title)

    if not window.Exists(5, 1):  # 等待最多5秒，1秒检查一次
        print(f"未找到标题为 '{window_title}' 的窗口")
        print("请确保通讯录管理窗口已打开")
        print("\n请按以下步骤操作：")
        print("1. 打开微信")
        print("2. 点击左下角的设置按钮")
        print("3. 选择'通用设置'")
        print("4. 点击'功能'")
        print("5. 找到并点击'通讯录管理'")
        return

    # 等待窗口完全加载
    if not wait_for_window_ready(window):
        print("窗口未能完全加载，程序可能无法正常工作")
        return
    
    # 窗口找到了，打印基本信息
    print(f"\n找到窗口: {window.Name}")
    print(f"窗口类名: {window.ClassName}")
    print(f"窗口位置: {window.BoundingRectangle}")
    
    # 保存窗口控件结构到JSON文件
    save_control_structure(window)
    
    # 尝试激活窗口
    try:
        window.SetFocus()
        time.sleep(0.5)  # 给窗口一些时间来响应
        print("已激活窗口")
    except Exception as e:
        print(f"激活窗口时出错: {e}")
        print("将继续尝试获取信息")
    
    # 打印窗口的详细控件结构
    print("\n=== 窗口控件结构 ===")
    print_control_info(window, max_depth=3)
    
    # 查找联系人列表
    contact_list = find_contact_list(window)
    
    if not contact_list:
        print("未找到联系人列表控件")
        return
    
    # 提取联系人信息
    contacts = scroll_and_extract(contact_list)
    
    # 打印联系人信息摘要
    if contacts:
        print_summary(contacts)

        # 保存到CSV文件
        df = pd.DataFrame(contacts)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"微信联系人_{timestamp}.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n已将 {len(contacts)} 个联系人保存到 {output_file}")

        # 同时保存一份最新的文件（覆盖旧文件）
        latest_file = "微信联系人_最新.csv"
        df.to_csv(latest_file, index=False, encoding='utf-8-sig')
        print(f"同时保存最新版本到 {latest_file}")

        # 保存详细的JSON格式文件，包含所有信息
        json_file = f"微信联系人_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(contacts, f, ensure_ascii=False, indent=2)
        print(f"详细信息已保存到 {json_file}")

    else:
        print("\n未提取到任何联系人信息")
        print("可能的原因：")
        print("1. 通讯录管理窗口未正确打开")
        print("2. 窗口结构发生变化")
        print("3. 联系人列表为空")
        print("4. 程序权限不足")
    
    print(f"\n提取完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()