{"ControlType": 50032, "ControlTypeName": "WindowControl", "Name": "通讯录管理", "ClassName": "ContactManagerWindow", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": -2, "right": 3840, "bottom": 1080, "width": 1920, "height": 1082}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "popupshadow", "AutomationId": "", "BoundingRectangle": {"left": -3000, "top": -3000, "right": -2900, "bottom": -2900, "width": 100, "height": 100}, "IsEnabled": false, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": -2, "right": 3840, "bottom": 1080, "width": 1920, "height": 1082}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": -2, "right": 3840, "bottom": 1080, "width": 1920, "height": 1082}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": -2, "right": 3840, "bottom": 1080, "width": 1920, "height": 1082}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": -2, "right": 2120, "bottom": 1080, "width": 200, "height": 1082}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "通讯录管理", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1932, "top": 4, "right": 2120, "bottom": 30, "width": 188, "height": 26}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50018, "ControlTypeName": "TabControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 50, "right": 2120, "bottom": 84, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50019, "ControlTypeName": "TabItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 50, "right": 2120, "bottom": 84, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "全部", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 50, "right": 2120, "bottom": 84, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 50, "right": 2120, "bottom": 84, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "全部", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 50, "right": 1960, "bottom": 84, "width": 40, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "(2731)", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1960, "top": 50, "right": 2011, "bottom": 84, "width": 51, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 96, "right": 2120, "bottom": 1080, "width": 200, "height": 984}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "筛选", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1932, "top": 96, "right": 2120, "bottom": 122, "width": 188, "height": 26}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "朋友权限", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 123, "right": 2120, "bottom": 157, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 123, "right": 2120, "bottom": 157, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 123, "right": 2120, "bottom": 157, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 123, "right": 2100, "bottom": 157, "width": 180, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2100, "top": 131, "right": 2108, "bottom": 147, "width": 8, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "朋友权限", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 123, "right": 2120, "bottom": 157, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "标签", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 157, "right": 2120, "bottom": 191, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 157, "right": 2120, "bottom": 191, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 157, "right": 2120, "bottom": 191, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 157, "right": 2100, "bottom": 191, "width": 180, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2100, "top": 165, "right": 2108, "bottom": 181, "width": 8, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "标签", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 157, "right": 2120, "bottom": 191, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "最近群聊", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 191, "right": 2120, "bottom": 225, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 191, "right": 2120, "bottom": 225, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 191, "right": 2120, "bottom": 225, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 191, "right": 2100, "bottom": 225, "width": 180, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2100, "top": 199, "right": 2108, "bottom": 215, "width": 8, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "最近群聊", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 1920, "top": 191, "right": 2120, "bottom": 225, "width": 200, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2120, "top": -2, "right": 2121, "bottom": 1080, "width": 1, "height": 1082}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": -2, "right": 3840, "bottom": 1080, "width": 1719, "height": 1082}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 6, "right": 2335, "bottom": 30, "width": 190, "height": 24}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 6, "right": 2335, "bottom": 30, "width": 190, "height": 24}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 6, "right": 2335, "bottom": 30, "width": 190, "height": 24}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 6, "right": 2335, "bottom": 30, "width": 190, "height": 24}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50004, "ControlTypeName": "EditControl", "Name": "搜索", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2165, "top": 6, "right": 2335, "bottom": 30, "width": 170, "height": 24}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2149, "top": 11, "right": 2165, "bottom": 27, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 42, "right": 3840, "bottom": 1080, "width": 1719, "height": 1038}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 42, "right": 3840, "bottom": 1080, "width": 1719, "height": 1038}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 42, "right": 3840, "bottom": 1080, "width": 1719, "height": 1038}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 42, "right": 3840, "bottom": 1080, "width": 1719, "height": 1038}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 42, "right": 3840, "bottom": 76, "width": 1719, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 42, "right": 3840, "bottom": 76, "width": 1719, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 42, "right": 2145, "bottom": 76, "width": 24, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 50, "right": 2170, "bottom": 66, "width": 25, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2170, "top": 49, "right": 2171, "bottom": 69, "width": 1, "height": 20}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "昵称", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 42, "right": 2692, "bottom": 76, "width": 516, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2692, "top": 49, "right": 2693, "bottom": 69, "width": 1, "height": 20}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "备注", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2698, "top": 42, "right": 3214, "bottom": 76, "width": 516, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3214, "top": 49, "right": 3215, "bottom": 69, "width": 1, "height": 20}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3220, "top": 42, "right": 3737, "bottom": 76, "width": 517, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "标签", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3226, "top": 42, "right": 3259, "bottom": 76, "width": 33, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3737, "top": 49, "right": 3738, "bottom": 69, "width": 1, "height": 20}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3743, "top": 42, "right": 3840, "bottom": 76, "width": 97, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "朋友权限", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3749, "top": 42, "right": 3805, "bottom": 76, "width": 56, "height": 34}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 76, "right": 3840, "bottom": 77, "width": 1719, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50008, "ControlTypeName": "ListControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 77, "right": 3840, "bottom": 1080, "width": 1719, "height": 1003}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 77, "right": 3840, "bottom": 129, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 77, "right": 3840, "bottom": 129, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 77, "right": 3840, "bottom": 128, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 92, "right": 2161, "bottom": 108, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 77, "right": 2687, "bottom": 128, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 85, "right": 2206, "bottom": 115, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 77, "right": 2687, "bottom": 128, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 77, "right": 3213, "bottom": 128, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 77, "right": 3203, "bottom": 128, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "添加备注", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 77, "right": 3203, "bottom": 128, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 77, "right": 3740, "bottom": 128, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 77, "right": 3730, "bottom": 128, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "添加标签", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 77, "right": 3730, "bottom": 128, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 77, "right": 3840, "bottom": 128, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 77, "right": 3840, "bottom": 128, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "修改权限", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3748, "top": 90, "right": 3840, "bottom": 112, "width": 92, "height": 22}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 128, "right": 3824, "bottom": 129, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 129, "right": 3840, "bottom": 181, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 129, "right": 3840, "bottom": 181, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 129, "right": 3840, "bottom": 180, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 144, "right": 2161, "bottom": 160, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 129, "right": 2687, "bottom": 180, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A~小游", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 137, "right": 2206, "bottom": 167, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A~", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 129, "right": 2687, "bottom": 180, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 129, "right": 3213, "bottom": 180, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 129, "right": 3203, "bottom": 180, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A~小游", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 129, "right": 3203, "bottom": 180, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 129, "right": 3740, "bottom": 180, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 129, "right": 3730, "bottom": 180, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 129, "right": 3730, "bottom": 180, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 129, "right": 3840, "bottom": 180, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 129, "right": 3840, "bottom": 180, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 180, "right": 3824, "bottom": 181, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 181, "right": 3840, "bottom": 233, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 181, "right": 3840, "bottom": 233, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 181, "right": 3840, "bottom": 232, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 196, "right": 2161, "bottom": 212, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 181, "right": 2687, "bottom": 232, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "陈圣林妈妈", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 189, "right": 2206, "bottom": 219, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A00达人视界眼镜小杨", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 181, "right": 2687, "bottom": 232, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 181, "right": 3213, "bottom": 232, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 181, "right": 3203, "bottom": 232, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "陈圣林妈妈", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 181, "right": 3203, "bottom": 232, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 181, "right": 3740, "bottom": 232, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 181, "right": 3730, "bottom": 232, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 181, "right": 3730, "bottom": 232, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 181, "right": 3840, "bottom": 232, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 181, "right": 3840, "bottom": 232, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 232, "right": 3824, "bottom": 233, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 233, "right": 3840, "bottom": 285, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 233, "right": 3840, "bottom": 285, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 233, "right": 3840, "bottom": 284, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 248, "right": 2161, "bottom": 264, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 233, "right": 2687, "bottom": 284, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A00~广西玉林容县  行行通", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 241, "right": 2206, "bottom": 271, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A00~广西玉林容县  行行通", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 233, "right": 2687, "bottom": 284, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 233, "right": 3213, "bottom": 284, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 233, "right": 3203, "bottom": 284, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 233, "right": 3203, "bottom": 284, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 233, "right": 3740, "bottom": 284, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 233, "right": 3730, "bottom": 284, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 233, "right": 3730, "bottom": 284, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 233, "right": 3840, "bottom": 284, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 233, "right": 3840, "bottom": 284, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 284, "right": 3824, "bottom": 285, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 285, "right": 3840, "bottom": 337, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 285, "right": 3840, "bottom": 337, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 285, "right": 3840, "bottom": 336, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 300, "right": 2161, "bottom": 316, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 285, "right": 2687, "bottom": 336, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "色彩汽修", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 293, "right": 2206, "bottom": 323, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A00色彩汽修", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 285, "right": 2687, "bottom": 336, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 285, "right": 3213, "bottom": 336, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 285, "right": 3203, "bottom": 336, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "色彩汽修", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 285, "right": 3203, "bottom": 336, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 285, "right": 3740, "bottom": 336, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 285, "right": 3730, "bottom": 336, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 285, "right": 3730, "bottom": 336, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 285, "right": 3840, "bottom": 336, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 285, "right": 3840, "bottom": 336, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 336, "right": 3824, "bottom": 337, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 337, "right": 3840, "bottom": 389, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 337, "right": 3840, "bottom": 389, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 337, "right": 3840, "bottom": 388, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 352, "right": 2161, "bottom": 368, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 337, "right": 2687, "bottom": 388, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A00星研丽美容院(容县邻里中心）", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 345, "right": 2206, "bottom": 375, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A00星研丽美容院(容县邻里中心）", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 337, "right": 2687, "bottom": 388, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 337, "right": 3213, "bottom": 388, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 337, "right": 3203, "bottom": 388, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 337, "right": 3203, "bottom": 388, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 337, "right": 3740, "bottom": 388, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 337, "right": 3730, "bottom": 388, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 337, "right": 3730, "bottom": 388, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 337, "right": 3840, "bottom": 388, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 337, "right": 3840, "bottom": 388, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 388, "right": 3824, "bottom": 389, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 389, "right": 3840, "bottom": 441, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 389, "right": 3840, "bottom": 441, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 389, "right": 3840, "bottom": 440, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 404, "right": 2161, "bottom": 420, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 389, "right": 2687, "bottom": 440, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A0沈瑞瑞", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 397, "right": 2206, "bottom": 427, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A0沈瑞瑞", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 389, "right": 2687, "bottom": 440, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 389, "right": 3213, "bottom": 440, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 389, "right": 3203, "bottom": 440, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 389, "right": 3203, "bottom": 440, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 389, "right": 3740, "bottom": 440, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 389, "right": 3730, "bottom": 440, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 389, "right": 3730, "bottom": 440, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 389, "right": 3840, "bottom": 440, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 389, "right": 3840, "bottom": 440, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 440, "right": 3824, "bottom": 441, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 441, "right": 3840, "bottom": 493, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 441, "right": 3840, "bottom": 493, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 441, "right": 3840, "bottom": 492, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 456, "right": 2161, "bottom": 472, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 441, "right": 2687, "bottom": 492, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A0兄弟广信装饰材料，刘勇军", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 449, "right": 2206, "bottom": 479, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A0兄弟广信装饰材料，刘勇军", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 441, "right": 2687, "bottom": 492, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 441, "right": 3213, "bottom": 492, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 441, "right": 3203, "bottom": 492, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 441, "right": 3203, "bottom": 492, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 441, "right": 3740, "bottom": 492, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 441, "right": 3730, "bottom": 492, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 441, "right": 3730, "bottom": 492, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 441, "right": 3840, "bottom": 492, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 441, "right": 3840, "bottom": 492, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 492, "right": 3824, "bottom": 493, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 493, "right": 3840, "bottom": 545, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 493, "right": 3840, "bottom": 545, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 493, "right": 3840, "bottom": 544, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 508, "right": 2161, "bottom": 524, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 493, "right": 2687, "bottom": 544, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "飞瑞体育-抖音体测系统3.0", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 501, "right": 2206, "bottom": 531, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A0～°yuจุ๊บ", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 493, "right": 2687, "bottom": 544, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 493, "right": 3213, "bottom": 544, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 493, "right": 3203, "bottom": 544, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "飞瑞体育-抖音体测系统3.0", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 493, "right": 3203, "bottom": 544, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 493, "right": 3740, "bottom": 544, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 493, "right": 3730, "bottom": 544, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "抖音，体测", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 493, "right": 3730, "bottom": 544, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 493, "right": 3840, "bottom": 544, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 493, "right": 3840, "bottom": 544, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 544, "right": 3824, "bottom": 545, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 545, "right": 3840, "bottom": 597, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 545, "right": 3840, "bottom": 597, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 545, "right": 3840, "bottom": 596, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 560, "right": 2161, "bottom": 576, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 545, "right": 2687, "bottom": 596, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "云端百里-抖音群发软件", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 553, "right": 2206, "bottom": 583, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A0云端获客百宝箱", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 545, "right": 2687, "bottom": 596, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 545, "right": 3213, "bottom": 596, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 545, "right": 3203, "bottom": 596, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "云端百里-抖音群发软件", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 545, "right": 3203, "bottom": 596, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 545, "right": 3740, "bottom": 596, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 545, "right": 3730, "bottom": 596, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 545, "right": 3730, "bottom": 596, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 545, "right": 3840, "bottom": 596, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 545, "right": 3840, "bottom": 596, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 596, "right": 3824, "bottom": 597, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 597, "right": 3840, "bottom": 649, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 597, "right": 3840, "bottom": 649, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 597, "right": 3840, "bottom": 648, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 612, "right": 2161, "bottom": 628, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 597, "right": 2687, "bottom": 648, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "陈泽彬妈妈(四幼)", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 605, "right": 2206, "bottom": 635, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A ৡ梦࿐", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 597, "right": 2687, "bottom": 648, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 597, "right": 3213, "bottom": 648, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 597, "right": 3203, "bottom": 648, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "陈泽彬妈妈(四幼)", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 597, "right": 3203, "bottom": 648, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 597, "right": 3740, "bottom": 648, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 597, "right": 3730, "bottom": 648, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 597, "right": 3730, "bottom": 648, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 597, "right": 3840, "bottom": 648, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 597, "right": 3840, "bottom": 648, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 648, "right": 3824, "bottom": 649, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 649, "right": 3840, "bottom": 701, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 649, "right": 3840, "bottom": 701, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 649, "right": 3840, "bottom": 700, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 664, "right": 2161, "bottom": 680, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 649, "right": 2687, "bottom": 700, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A🏠覃莉雯18277158891", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 657, "right": 2206, "bottom": 687, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A🇨🇳小覃", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 649, "right": 2687, "bottom": 700, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 649, "right": 3213, "bottom": 700, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 649, "right": 3203, "bottom": 700, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A🏠覃莉雯18277158891", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 649, "right": 3203, "bottom": 700, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 649, "right": 3740, "bottom": 700, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 649, "right": 3730, "bottom": 700, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 649, "right": 3730, "bottom": 700, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 649, "right": 3840, "bottom": 700, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 649, "right": 3840, "bottom": 700, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 700, "right": 3824, "bottom": 701, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 701, "right": 3840, "bottom": 753, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 701, "right": 3840, "bottom": 753, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 701, "right": 3840, "bottom": 752, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 716, "right": 2161, "bottom": 732, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 701, "right": 2687, "bottom": 752, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "卢易妈妈", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 709, "right": 2206, "bottom": 739, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A🍃阿玉", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 701, "right": 2687, "bottom": 752, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 701, "right": 3213, "bottom": 752, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 701, "right": 3203, "bottom": 752, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "卢易妈妈", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 701, "right": 3203, "bottom": 752, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 701, "right": 3740, "bottom": 752, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 701, "right": 3730, "bottom": 752, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 701, "right": 3730, "bottom": 752, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 701, "right": 3840, "bottom": 752, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 701, "right": 3840, "bottom": 752, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 752, "right": 3824, "bottom": 753, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 753, "right": 3840, "bottom": 805, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 753, "right": 3840, "bottom": 805, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 753, "right": 3840, "bottom": 804, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 768, "right": 2161, "bottom": 784, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 753, "right": 2687, "bottom": 804, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "绣江茗城@余诗欣18877521729", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 761, "right": 2206, "bottom": 791, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A  🏡宽华城御景学府余诗欣", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 753, "right": 2687, "bottom": 804, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 753, "right": 3213, "bottom": 804, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 753, "right": 3203, "bottom": 804, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "绣江茗城@余诗欣18877521729", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 753, "right": 3203, "bottom": 804, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 753, "right": 3740, "bottom": 804, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 753, "right": 3730, "bottom": 804, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 753, "right": 3730, "bottom": 804, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 753, "right": 3840, "bottom": 804, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 753, "right": 3840, "bottom": 804, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 804, "right": 3824, "bottom": 805, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 805, "right": 3840, "bottom": 857, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 805, "right": 3840, "bottom": 857, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 805, "right": 3840, "bottom": 856, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 820, "right": 2161, "bottom": 836, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 805, "right": 2687, "bottom": 856, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "A 萍13827107065妇幼鸭脚煲", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 813, "right": 2206, "bottom": 843, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A 萍13827107065妇幼鸭脚煲", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 805, "right": 2687, "bottom": 856, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 805, "right": 3213, "bottom": 856, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 805, "right": 3203, "bottom": 856, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 805, "right": 3203, "bottom": 856, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 805, "right": 3740, "bottom": 856, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 805, "right": 3730, "bottom": 856, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 805, "right": 3730, "bottom": 856, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 805, "right": 3840, "bottom": 856, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 805, "right": 3840, "bottom": 856, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 856, "right": 3824, "bottom": 857, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 857, "right": 3840, "bottom": 909, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 857, "right": 3840, "bottom": 909, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 857, "right": 3840, "bottom": 908, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 872, "right": 2161, "bottom": 888, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 857, "right": 2687, "bottom": 908, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "李明骏妈妈", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 865, "right": 2206, "bottom": 895, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "A雨梦娟儿🦋🦋🦋", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 857, "right": 2687, "bottom": 908, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 857, "right": 3213, "bottom": 908, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 857, "right": 3203, "bottom": 908, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "李明骏妈妈", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 857, "right": 3203, "bottom": 908, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 857, "right": 3740, "bottom": 908, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 857, "right": 3730, "bottom": 908, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 857, "right": 3730, "bottom": 908, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 857, "right": 3840, "bottom": 908, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 857, "right": 3840, "bottom": 908, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 908, "right": 3824, "bottom": 909, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 909, "right": 3840, "bottom": 961, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 909, "right": 3840, "bottom": 961, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 909, "right": 3840, "bottom": 960, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 924, "right": 2161, "bottom": 940, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 909, "right": 2687, "bottom": 960, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "啊啊啊啊滨", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 917, "right": 2206, "bottom": 947, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "啊啊啊啊滨", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 909, "right": 2687, "bottom": 960, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 909, "right": 3213, "bottom": 960, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 909, "right": 3203, "bottom": 960, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 909, "right": 3203, "bottom": 960, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 909, "right": 3740, "bottom": 960, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 909, "right": 3730, "bottom": 960, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "体测，抖音", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 909, "right": 3730, "bottom": 960, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 909, "right": 3840, "bottom": 960, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 909, "right": 3840, "bottom": 960, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 960, "right": 3824, "bottom": 961, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 961, "right": 3840, "bottom": 1013, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 961, "right": 3840, "bottom": 1013, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 961, "right": 3840, "bottom": 1012, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 976, "right": 2161, "bottom": 992, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 961, "right": 2687, "bottom": 1012, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "Aaaa JC 花式篮球Freestyle", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 969, "right": 2206, "bottom": 999, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "Aaaa JC 花式篮球 Freestyle", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 961, "right": 2687, "bottom": 1012, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 961, "right": 3213, "bottom": 1012, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 961, "right": 3203, "bottom": 1012, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "Aaaa JC 花式篮球Freestyle", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 961, "right": 3203, "bottom": 1012, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 961, "right": 3740, "bottom": 1012, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 961, "right": 3730, "bottom": 1012, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 961, "right": 3730, "bottom": 1012, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 961, "right": 3840, "bottom": 1012, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 961, "right": 3840, "bottom": 1012, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 1012, "right": 3824, "bottom": 1013, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 1013, "right": 3840, "bottom": 1065, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 1013, "right": 3840, "bottom": 1065, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 1013, "right": 3840, "bottom": 1064, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 1028, "right": 2161, "bottom": 1044, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 1013, "right": 2687, "bottom": 1064, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "容县视创广告装饰黄明林", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 1021, "right": 2206, "bottom": 1051, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "AAA-广告装饰👉黄小洋👈", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 1013, "right": 2687, "bottom": 1064, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 1013, "right": 3213, "bottom": 1064, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 1013, "right": 3203, "bottom": 1064, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "容县视创广告装饰黄明林", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 1013, "right": 3203, "bottom": 1064, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 1013, "right": 3740, "bottom": 1064, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 1013, "right": 3730, "bottom": 1064, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 1013, "right": 3730, "bottom": 1064, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 1013, "right": 3840, "bottom": 1064, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 1013, "right": 3840, "bottom": 1064, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 1064, "right": 3824, "bottom": 1065, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50007, "ControlTypeName": "ListItemControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 1065, "right": 3840, "bottom": 1117, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 1065, "right": 3840, "bottom": 1117, "width": 1719, "height": 52}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2121, "top": 1065, "right": 3840, "bottom": 1116, "width": 1719, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50002, "ControlTypeName": "CheckBoxControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2145, "top": 1080, "right": 2161, "bottom": 1096, "width": 16, "height": 16}, "IsEnabled": true, "IsOffscreen": true, "Children": []}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2161, "top": 1065, "right": 2687, "bottom": 1116, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "AAALily", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2176, "top": 1073, "right": 2206, "bottom": 1103, "width": 30, "height": 30}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50020, "ControlTypeName": "TextControl", "Name": "AAALily", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2218, "top": 1065, "right": 2687, "bottom": 1116, "width": 469, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2687, "top": 1065, "right": 3213, "bottom": 1116, "width": 526, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 1065, "right": 3203, "bottom": 1116, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2697, "top": 1065, "right": 3203, "bottom": 1116, "width": 506, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3213, "top": 1065, "right": 3740, "bottom": 1116, "width": 527, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 1065, "right": 3730, "bottom": 1116, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3223, "top": 1065, "right": 3730, "bottom": 1116, "width": 507, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 1065, "right": 3840, "bottom": 1116, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3740, "top": 1065, "right": 3840, "bottom": 1116, "width": 100, "height": 51}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}, {"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 2221, "top": 1116, "right": 3824, "bottom": 1117, "width": 1603, "height": 1}, "IsEnabled": true, "IsOffscreen": true, "Children": []}]}]}]}]}]}]}]}]}]}]}, {"ControlType": 50021, "ControlTypeName": "ToolBarControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3704, "top": -2, "right": 3840, "bottom": 24, "width": 136, "height": 26}, "IsEnabled": true, "IsOffscreen": false, "Children": [{"ControlType": 50033, "ControlTypeName": "PaneControl", "Name": "", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3704, "top": -2, "right": 3741, "bottom": 24, "width": 37, "height": 26}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "最小化", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3741, "top": -2, "right": 3774, "bottom": 23, "width": 33, "height": 25}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "向下还原", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3774, "top": -2, "right": 3807, "bottom": 23, "width": 33, "height": 25}, "IsEnabled": true, "IsOffscreen": false, "Children": []}, {"ControlType": 50000, "ControlTypeName": "ButtonControl", "Name": "关闭", "ClassName": "", "AutomationId": "", "BoundingRectangle": {"left": 3807, "top": -2, "right": 3840, "bottom": 23, "width": 33, "height": 25}, "IsEnabled": true, "IsOffscreen": false, "Children": []}]}]}]}